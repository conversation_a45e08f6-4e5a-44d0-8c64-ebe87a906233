<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="container-fluid px-4 py-3">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header-card bg-white rounded-4 shadow-sm p-4 mb-4">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="page-title-section mb-3 mb-lg-0">
                        <div class="d-flex align-items-center mb-2">
                            <div class="page-icon-wrapper me-3">
                                <i class="bi bi-camera-video text-primary"></i>
                            </div>
                            <div>
                                <h1 class="page-title mb-1">摄像头管理</h1>
                                <p class="page-subtitle text-muted mb-0">管理和监控所有摄像头设备，实时查看视频流和控制摄像头</p>
                            </div>
                        </div>
                    </div>
                    <div class="page-actions d-flex flex-wrap gap-3">
                        <div class="search-wrapper">
                            <div class="input-group search-input-group">
                                <span class="input-group-text bg-white border-end-0">
                                    <i class="bi bi-search text-muted"></i>
                                </span>
                                <input type="text" class="form-control border-start-0 ps-0" placeholder="搜索摄像头..." id="cameraSearchInput">
                            </div>
                        </div>
                        <button class="btn btn-primary btn-add-camera rounded-pill px-4 d-flex align-items-center" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                            <i class="bi bi-plus-lg me-2"></i> 添加摄像头
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 流服务器状态提示 -->
    <c:if test="${not empty streamServerMessage}">
        <div class="row mb-4">
            <div class="col-12">
                <div class="alert alert-${streamServerStatus == 'success' ? 'success' : (streamServerStatus == 'warning' ? 'warning' : 'danger')} alert-dismissible fade show rounded-4 shadow-sm border-0" role="alert">
                    <div class="d-flex align-items-center">
                        <div class="alert-icon-wrapper me-3">
                            <i class="bi bi-${streamServerStatus == 'success' ? 'check-circle-fill' : (streamServerStatus == 'warning' ? 'exclamation-triangle-fill' : 'x-circle-fill')} fs-4"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="alert-heading mb-1 fw-bold">视频流服务器状态</h6>
                            <p class="mb-0 opacity-90">${streamServerMessage}</p>
                        </div>
                        <div class="ms-3">
                            <button type="button" class="btn btn-sm btn-outline-${streamServerStatus == 'success' ? 'success' : (streamServerStatus == 'warning' ? 'warning' : 'danger')} rounded-pill" onclick="checkStreamServerStatus()">
                                <i class="bi bi-arrow-clockwise me-1"></i> 检查状态
                            </button>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </c:if>



    <!-- 摄像头统计卡片 -->
    <div class="stats-section mb-4">
        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="card stats-card bg-primary text-white border-0">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="stats-content">
                                <div class="stats-icon-container mb-3">
                                    <i class="bi bi-camera-video stats-icon"></i>
                                </div>
                                <h3 class="stats-number mb-1">${cameraStats.total}</h3>
                                <p class="stats-label mb-0">摄像头总数</p>
                            </div>
                            <div class="stats-trend">
                                <i class="bi bi-arrow-up-right"></i>
                                <span class="trend-text">+5%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card stats-card bg-success text-white border-0">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="stats-content">
                                <div class="stats-icon-container mb-3">
                                    <i class="bi bi-wifi stats-icon"></i>
                                </div>
                                <h3 class="stats-number mb-1">${cameraStats.online}</h3>
                                <p class="stats-label mb-0">在线摄像头</p>
                            </div>
                            <div class="stats-trend">
                                <i class="bi bi-arrow-up-right"></i>
                                <span class="trend-text">${cameraStats.total > 0 ? Math.round(cameraStats.online * 100 / cameraStats.total) : 0}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="card stats-card bg-danger text-white border-0">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="stats-content">
                                <div class="stats-icon-container mb-3">
                                    <i class="bi bi-wifi-off stats-icon"></i>
                                </div>
                                <h3 class="stats-number mb-1">${cameraStats.offline}</h3>
                                <p class="stats-label mb-0">离线摄像头</p>
                            </div>
                            <div class="stats-trend">
                                <i class="bi bi-arrow-down-right"></i>
                                <span class="trend-text">检查</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-section mb-4">
        <div class="filter-toolbar bg-white rounded-4 shadow-sm p-4">
            <div class="row g-3 align-items-center">
                <div class="col-auto">
                    <h6 class="filter-section-title mb-0 text-muted fw-bold">
                        <i class="bi bi-funnel me-2"></i>筛选条件
                    </h6>
                </div>
                <div class="col-md-auto">
                    <div class="filter-group">
                        <span class="filter-label me-3">状态:</span>
                        <div class="btn-group filter-btn-group" role="group">
                            <button class="filter-btn active" data-filter="all">全部</button>
                            <button class="filter-btn" data-filter="online">在线</button>
                            <button class="filter-btn" data-filter="offline">离线</button>
                        </div>
                    </div>
                </div>
                <div class="col-md-auto">
                    <div class="filter-group">
                        <span class="filter-label me-3">位置:</span>
                        <div class="btn-group filter-btn-group" role="group">
                            <button class="filter-btn active" data-location="all">全部</button>
                            <c:set var="locations" value="" />
                            <c:forEach items="${cameras}" var="camera">
                                <c:if test="${not empty camera.location && !locations.contains(camera.location)}">
                                    <c:set var="locations" value="${locations}${camera.location}," />
                                    <button class="filter-btn" data-location="${camera.location}">${camera.location}</button>
                                </c:if>
                            </c:forEach>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 摄像头列表 -->
    <div class="cameras-section mb-5">
        <div class="row g-4" id="cameraList">
            <c:forEach items="${cameras}" var="camera">
                <div class="col-xl-4 col-lg-6 col-md-6 camera-item"
                     data-status="${camera.status == 1 ? 'online' : 'offline'}"
                     data-location="${camera.location}">
                    <div class="card camera-card h-100 border-0">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0 camera-name">
                                <span class="camera-status ${camera.status == 1 ? 'camera-online' : 'camera-offline'}"></span>
                                ${camera.name}
                            </h5>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary rounded-circle" type="button" id="dropdownMenuButton${camera.id}" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="dropdownMenuButton${camera.id}">
                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="viewCameraDetail(${camera.id})"><i class="bi bi-info-circle me-2"></i>查看详情</a></li>
                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="viewStream(${camera.id})"><i class="bi bi-play-circle me-2"></i>查看视频流</a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <c:if test="${camera.status == 0}">
                                        <li><a class="dropdown-item text-success" href="javascript:void(0)" onclick="connectCamera(${camera.id})"><i class="bi bi-wifi me-2"></i>连接摄像头</a></li>
                                    </c:if>
                                    <c:if test="${camera.status == 1}">
                                        <li><a class="dropdown-item text-danger" href="javascript:void(0)" onclick="disconnectCamera(${camera.id})"><i class="bi bi-wifi-off me-2"></i>断开摄像头</a></li>
                                    </c:if>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="javascript:void(0)" onclick="deleteCamera(${camera.id}, '${camera.name}')"><i class="bi bi-trash me-2"></i>删除摄像头</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="camera-thumbnail mb-3">
                            <c:choose>
                                <c:when test="${camera.status == 1}">
                                    <img src="${pageContext.request.contextPath}/static/images/camera-preview.jpg" alt="摄像头预览">
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-success rounded-pill px-2 py-1">
                                            <i class="bi bi-broadcast me-1"></i> 在线
                                        </span>
                                    </div>
                                </c:when>
                                <c:otherwise>
                                    <i class="bi bi-camera-video-off camera-icon"></i>
                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-danger rounded-pill px-2 py-1">
                                            <i class="bi bi-wifi-off me-1"></i> 离线
                                        </span>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                        </div>
                        <div class="camera-info-item camera-location">
                            <i class="bi bi-geo-alt"></i>
                            <span>${not empty camera.location ? camera.location : '未设置位置'}</span>
                        </div>
                        <div class="camera-info-item">
                            <i class="bi bi-building"></i>
                            <span>
                                <c:choose>
                                    <c:when test="${camera.room != null}">
                                        ${camera.room.roomNumber} (${camera.room.floorNumber}楼)
                                    </c:when>
                                    <c:otherwise>
                                        <span class="text-muted">未分配房间</span>
                                    </c:otherwise>
                                </c:choose>
                            </span>
                        </div>
                        <div class="camera-info-item">
                            <i class="bi bi-tag"></i>
                            <span>${not empty camera.brand ? camera.brand : '未知品牌'} ${not empty camera.model ? camera.model : '未知型号'}</span>
                        </div>
                        <div class="camera-info-item">
                            <i class="bi bi-ethernet"></i>
                            <span>${camera.ipAddress}:${camera.port}</span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <button class="btn btn-camera-action btn-outline-primary" onclick="viewCameraDetail(${camera.id})">
                                <i class="bi bi-info-circle"></i> 详情
                            </button>
                            <button class="btn btn-camera-action ${camera.status == 1 ? 'btn-success' : 'btn-outline-secondary'}" onclick="viewStream(${camera.id})">
                                <i class="bi bi-play-circle"></i> 查看视频
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </c:forEach>

            <c:if test="${empty cameras}">
                <div class="col-12">
                    <div class="empty-state-card bg-white rounded-4 shadow-sm text-center py-5 px-4">
                        <div class="empty-state-icon mb-4">
                            <i class="bi bi-camera-video-off text-muted"></i>
                        </div>
                        <h4 class="empty-state-title mb-3">暂无摄像头设备</h4>
                        <p class="empty-state-description text-muted mb-4">
                            您还没有添加任何摄像头设备。点击下方按钮开始添加您的第一个摄像头设备，开始监控管理。
                        </p>
                        <button class="btn btn-primary btn-lg rounded-pill px-5" data-bs-toggle="modal" data-bs-target="#addCameraModal">
                            <i class="bi bi-plus-lg me-2"></i> 添加第一个摄像头
                        </button>
                    </div>
                </div>
            </c:if>
        </div>
    </div>

    <!-- 流服务器管理面板 -->
    <div class="server-management-section">
        <div class="card server-management-card border-0 shadow-sm rounded-4">
            <div class="card-header bg-gradient border-0 py-4">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div class="server-title-section mb-3 mb-lg-0">
                        <h5 class="card-title mb-1 fw-bold text-dark">
                            <i class="bi bi-server me-2 text-primary"></i>视频流服务器管理
                        </h5>
                        <p class="card-subtitle text-muted mb-0">管理和监控视频流转码服务器状态</p>
                    </div>
                    <div class="server-actions d-flex flex-wrap gap-2">
                        <button class="btn btn-sm btn-outline-primary rounded-pill" onclick="checkStreamServerStatus()">
                            <i class="bi bi-arrow-clockwise me-1"></i> 刷新状态
                        </button>
                        <button class="btn btn-sm btn-success rounded-pill" onclick="startStreamServer()">
                            <i class="bi bi-play-fill me-1"></i> 启动
                        </button>
                        <button class="btn btn-sm btn-warning rounded-pill" onclick="restartStreamServer()">
                            <i class="bi bi-arrow-clockwise me-1"></i> 重启
                        </button>
                        <button class="btn btn-sm btn-danger rounded-pill" onclick="stopStreamServer()">
                            <i class="bi bi-stop-fill me-1"></i> 停止
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body py-4">
                <div class="row align-items-center g-3">
                    <div class="col-lg-8">
                        <div class="server-status-info d-flex align-items-center">
                            <div class="status-indicator-wrapper me-3">
                                <span id="serverStatusIndicator" class="badge ${streamServerRunning ? 'bg-success' : 'bg-danger'} rounded-pill px-3 py-2 fs-6">
                                    <i class="bi bi-${streamServerRunning ? 'check-circle-fill' : 'x-circle-fill'} me-1"></i>
                                    ${streamServerRunning ? '运行中' : '未运行'}
                                </span>
                            </div>
                            <div class="server-details">
                                <div class="server-address mb-1">
                                    <i class="bi bi-globe me-2 text-muted"></i>
                                    <span class="text-muted">服务器地址:</span>
                                    <code class="ms-1">http://localhost:3001</code>
                                </div>
                                <div class="server-info">
                                    <i class="bi bi-info-circle me-2 text-muted"></i>
                                    <small class="text-muted">Node.js RTSP转码服务器</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="text-lg-end">
                            <button class="btn btn-outline-info rounded-pill" onclick="showStreamServerDetails()">
                                <i class="bi bi-info-circle me-1"></i> 查看详情
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast 提示容器 -->
<div class="toast-container"></div>

<!-- 删除摄像头确认模态框 -->
<div class="modal fade" id="deleteCameraModal" tabindex="-1" aria-labelledby="deleteCameraModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title fw-bold text-danger" id="deleteCameraModalLabel">
                    <i class="bi bi-exclamation-triangle me-2"></i>确认删除摄像头
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning d-flex align-items-center" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <div>
                        <strong>警告：</strong>此操作不可撤销！删除摄像头将会：
                    </div>
                </div>
                <ul class="list-unstyled mb-3">
                    <li><i class="bi bi-x-circle text-danger me-2"></i>永久删除摄像头配置信息</li>
                    <li><i class="bi bi-x-circle text-danger me-2"></i>停止所有相关的视频流</li>
                    <li><i class="bi bi-x-circle text-danger me-2"></i>清除相关的历史记录</li>
                </ul>
                <p class="mb-0">您确定要删除摄像头 <strong id="deleteCameraName"></strong> 吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary rounded-pill px-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-2"></i>取消
                </button>
                <button type="button" class="btn btn-danger rounded-pill px-4" id="confirmDeleteBtn">
                    <i class="bi bi-trash me-2"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 添加摄像头模态框 -->
<div class="modal fade" id="addCameraModal" tabindex="-1" aria-labelledby="addCameraModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title fw-bold" id="addCameraModalLabel">
                    <i class="bi bi-plus-circle me-2 text-primary"></i>添加摄像头
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addCameraForm" class="needs-validation" novalidate>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="name" name="name" placeholder="摄像头名称" required>
                                <label for="name">摄像头名称 <span class="text-danger">*</span></label>
                                <div class="invalid-feedback">请输入摄像头名称</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="location" name="location" placeholder="位置">
                                <label for="location">位置</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="ipAddress" name="ipAddress" placeholder="IP地址" required>
                                <label for="ipAddress">IP地址 <span class="text-danger">*</span></label>
                                <div class="invalid-feedback">请输入有效的IP地址</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="port" name="port" value="554" placeholder="端口号">
                                <label for="port">端口号</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" value="admin" placeholder="用户名">
                                <label for="username">用户名</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" value="admin" placeholder="密码">
                                <label for="password">密码</label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="rtspUrl" name="rtspUrl" placeholder="RTSP URL">
                                <label for="rtspUrl">RTSP URL (可选，留空将自动生成)</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="brand" name="brand" placeholder="品牌">
                                <label for="brand">品牌</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="model" name="model" placeholder="型号">
                                <label for="model">型号</label>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-floating">
                                <select class="form-select" id="roomId" name="roomId" aria-label="所属房间">
                                    <option value="">-- 选择房间 --</option>
                                    <c:forEach items="${rooms}" var="room">
                                        <option value="${room.id}">${room.roomNumber} (${room.floorNumber}楼)</option>
                                    </c:forEach>
                                </select>
                                <label for="roomId">所属房间</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary rounded-pill px-4" data-bs-dismiss="modal">
                    <i class="bi bi-x-lg me-2"></i>取消
                </button>
                <button type="submit" form="addCameraForm" class="btn btn-primary rounded-pill px-4">
                    <i class="bi bi-check-lg me-2"></i>添加
                </button>
            </div>
        </div>
    </div>
</div>
